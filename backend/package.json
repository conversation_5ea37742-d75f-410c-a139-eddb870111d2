{"name": "mcp-sql-backend", "version": "1.0.0", "description": "Backend API server for MySQL search using MCP", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.4", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "mysql", "mcp", "api"], "author": "Your Name", "license": "MIT"}