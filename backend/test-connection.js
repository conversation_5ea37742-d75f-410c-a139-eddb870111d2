/**
 * Test script to verify Supabase connection and setup
 * Run with: node test-connection.js
 */

const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  console.log(`📍 URL: ${supabaseUrl}`);
  console.log(`🔑 Key: ${supabaseKey.substring(0, 20)}...`);
  console.log('');

  try {
    // Test 1: Try to get table names using our custom function
    console.log('📋 Test 1: Getting table names...');
    const { data: tables, error: tablesError } = await supabase.rpc('get_table_names');
    
    if (tablesError) {
      console.log('⚠️  Custom function not found, using fallback method');
      console.log('   Error:', tablesError.message);
      
      // Fallback: Try to query a known table
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('users')
        .select('id')
        .limit(1);
      
      if (fallbackError) {
        console.error('❌ Fallback test failed:', fallbackError.message);
        console.log('');
        console.log('🔧 Setup required:');
        console.log('   1. Make sure you have run the schema.sql in Supabase SQL Editor');
        console.log('   2. Check that tables exist in your database');
        console.log('   3. Verify your credentials are correct');
        return false;
      } else {
        console.log('✅ Basic connection works, but helper functions missing');
        console.log('   Tables available: users, products, orders (assumed)');
      }
    } else {
      console.log('✅ Table names retrieved successfully:');
      tables.forEach(table => console.log(`   - ${table.table_name}`));
    }

    // Test 2: Try to get table structure
    console.log('');
    console.log('🏗️  Test 2: Getting table structure...');
    const { data: structure, error: structureError } = await supabase.rpc('get_table_structure', {
      table_name: 'users'
    });

    if (structureError) {
      console.log('⚠️  Structure function not found, using fallback');
      console.log('   Error:', structureError.message);
      
      // Fallback: Get sample data
      const { data: sampleData, error: sampleError } = await supabase
        .from('users')
        .select('*')
        .limit(1);
      
      if (sampleError) {
        console.error('❌ Sample data test failed:', sampleError.message);
        return false;
      } else {
        console.log('✅ Sample data retrieved, columns detected:');
        if (sampleData && sampleData.length > 0) {
          Object.keys(sampleData[0]).forEach(col => console.log(`   - ${col}`));
        }
      }
    } else {
      console.log('✅ Table structure retrieved successfully:');
      structure.forEach(col => console.log(`   - ${col.column_name} (${col.data_type})`));
    }

    // Test 3: Try a search operation
    console.log('');
    console.log('🔍 Test 3: Testing search functionality...');
    const { data: searchData, error: searchError } = await supabase
      .from('users')
      .select('*')
      .ilike('first_name', '%John%')
      .limit(5);

    if (searchError) {
      console.error('❌ Search test failed:', searchError.message);
      return false;
    } else {
      console.log(`✅ Search test successful, found ${searchData.length} results`);
      if (searchData.length > 0) {
        console.log(`   Sample result: ${searchData[0].first_name} ${searchData[0].last_name}`);
      }
    }

    console.log('');
    console.log('🎉 All tests passed! Your Supabase setup is working correctly.');
    console.log('');
    console.log('🚀 You can now start the application with: npm run dev');
    
    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    return false;
  }
}

// Run the test
testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
