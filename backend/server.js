/**
 * Express server for MySQL search application using MCP
 * Provides REST API endpoints for database operations
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { spawn } = require('child_process');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Global variable to store MCP process
let mcpProcess = null;

/**
 * Initialize MCP MySQL server process
 * @returns {Promise<boolean>} Success status
 */
async function initializeMCP() {
  try {
    console.log('Initializing MySQL MCP server...');
    
    // Set environment variables for MCP
    const env = {
      ...process.env,
      MYSQL_HOST: process.env.MYSQL_HOST || '127.0.0.1',
      MYSQL_PORT: process.env.MYSQL_PORT || '3306',
      MYSQL_USER: process.env.MYSQL_USER || 'root',
      MYSQL_PASS: process.env.MYSQL_PASS || '',
      MYSQL_DB: process.env.MYSQL_DB || 'test_db'
    };

    // Start MCP server process
    mcpProcess = spawn('npx', ['-y', '@kevinwatt/mysql-mcp'], {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    mcpProcess.stdout.on('data', (data) => {
      console.log(`MCP stdout: ${data}`);
    });

    mcpProcess.stderr.on('data', (data) => {
      console.error(`MCP stderr: ${data}`);
    });

    mcpProcess.on('close', (code) => {
      console.log(`MCP process exited with code ${code}`);
    });

    console.log('MCP server initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize MCP server:', error);
    return false;
  }
}

/**
 * Send command to MCP server and get response
 * @param {Object} command - MCP command object
 * @returns {Promise<Object>} Response from MCP server
 */
async function sendMCPCommand(command) {
  return new Promise((resolve, reject) => {
    if (!mcpProcess) {
      reject(new Error('MCP process not initialized'));
      return;
    }

    const timeout = setTimeout(() => {
      reject(new Error('MCP command timeout'));
    }, 30000);

    let responseData = '';
    
    const onData = (data) => {
      responseData += data.toString();
      try {
        const response = JSON.parse(responseData);
        clearTimeout(timeout);
        mcpProcess.stdout.removeListener('data', onData);
        resolve(response);
      } catch (e) {
        // Continue collecting data if JSON is incomplete
      }
    };

    mcpProcess.stdout.on('data', onData);
    mcpProcess.stdin.write(JSON.stringify(command) + '\n');
  });
}

// API Routes

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Server is running',
    mcpStatus: mcpProcess ? 'Connected' : 'Disconnected'
  });
});

/**
 * Get all tables in the database
 */
app.get('/api/tables', async (req, res) => {
  try {
    const command = {
      method: 'tools/call',
      params: {
        name: 'list_tables'
      }
    };

    const response = await sendMCPCommand(command);
    res.json({ success: true, data: response });
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch tables',
      message: error.message 
    });
  }
});

/**
 * Get table structure
 */
app.get('/api/tables/:tableName/structure', async (req, res) => {
  try {
    const { tableName } = req.params;
    
    const command = {
      method: 'tools/call',
      params: {
        name: 'describe_table',
        arguments: {
          table: tableName
        }
      }
    };

    const response = await sendMCPCommand(command);
    res.json({ success: true, data: response });
  } catch (error) {
    console.error('Error describing table:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to describe table',
      message: error.message 
    });
  }
});

/**
 * Search/query data from database
 */
app.post('/api/search', async (req, res) => {
  try {
    const { query, table, searchTerm, columns } = req.body;

    let sqlQuery;
    
    if (query) {
      // Direct SQL query
      sqlQuery = query;
    } else if (table && searchTerm) {
      // Build search query
      if (columns && columns.length > 0) {
        const whereClause = columns
          .map(col => `${col} LIKE '%${searchTerm}%'`)
          .join(' OR ');
        sqlQuery = `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 100`;
      } else {
        sqlQuery = `SELECT * FROM ${table} WHERE CONCAT_WS(' ', *) LIKE '%${searchTerm}%' LIMIT 100`;
      }
    } else {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid search parameters' 
      });
    }

    const command = {
      method: 'tools/call',
      params: {
        name: 'mysql_query',
        arguments: {
          sql: sqlQuery
        }
      }
    };

    const response = await sendMCPCommand(command);
    res.json({ success: true, data: response, query: sqlQuery });
  } catch (error) {
    console.error('Error executing search:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to execute search',
      message: error.message 
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error',
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found' 
  });
});

// Start server
async function startServer() {
  try {
    // Initialize MCP connection
    await initializeMCP();
    
    app.listen(PORT, () => {
      console.log(`Server running on http://localhost:${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  if (mcpProcess) {
    mcpProcess.kill();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down server...');
  if (mcpProcess) {
    mcpProcess.kill();
  }
  process.exit(0);
});

startServer();
