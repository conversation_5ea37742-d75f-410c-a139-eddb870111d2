/**
 * Express server for PostgreSQL search application using Supabase
 * Provides REST API endpoints for database operations
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test Supabase connection
 * @returns {Promise<boolean>} Success status
 */
async function testSupabaseConnection() {
  try {
    console.log('Testing Supabase connection...');

    // Test connection by trying to fetch from a system table
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);

    if (error) {
      console.error('Supabase connection test failed:', error.message);
      return false;
    }

    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Failed to connect to Supabase:', error.message);
    return false;
  }
}

/**
 * Get list of tables from the database
 * @returns {Promise<Array>} Array of table names
 */
async function getTables() {
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_type', 'BASE TABLE');

    if (error) {
      throw new Error(error.message);
    }

    return data.map(row => row.table_name);
  } catch (error) {
    console.error('Error fetching tables:', error);
    throw error;
  }
}

/**
 * Get table structure/schema
 * @param {string} tableName - Name of the table
 * @returns {Promise<Array>} Array of column information
 */
async function getTableStructure(tableName) {
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error fetching table structure:', error);
    throw error;
  }
}

// API Routes

/**
 * Health check endpoint
 */
app.get('/api/health', async (req, res) => {
  try {
    const isConnected = await testSupabaseConnection();
    res.json({
      status: 'OK',
      message: 'Server is running',
      supabaseStatus: isConnected ? 'Connected' : 'Disconnected'
    });
  } catch (error) {
    res.json({
      status: 'OK',
      message: 'Server is running',
      supabaseStatus: 'Error',
      error: error.message
    });
  }
});

/**
 * Get all tables in the database
 */
app.get('/api/tables', async (req, res) => {
  try {
    const tables = await getTables();
    res.json({
      success: true,
      data: tables,
      count: tables.length
    });
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tables',
      message: error.message
    });
  }
});

/**
 * Get table structure
 */
app.get('/api/tables/:tableName/structure', async (req, res) => {
  try {
    const { tableName } = req.params;

    // Validate table name to prevent SQL injection
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid table name'
      });
    }

    const structure = await getTableStructure(tableName);
    res.json({
      success: true,
      data: structure,
      tableName: tableName
    });
  } catch (error) {
    console.error('Error describing table:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to describe table',
      message: error.message
    });
  }
});

/**
 * Search/query data from database
 */
app.post('/api/search', async (req, res) => {
  try {
    const { table, searchTerm, columns } = req.body;

    // Validate required parameters
    if (!table || !searchTerm) {
      return res.status(400).json({
        success: false,
        error: 'Table name and search term are required'
      });
    }

    // Validate table name to prevent SQL injection
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(table)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid table name'
      });
    }

    // Sanitize search term
    const sanitizedSearchTerm = searchTerm.replace(/'/g, "''");

    let query;

    if (columns && columns.length > 0) {
      // Search in specific columns
      const validColumns = columns.filter(col => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(col));
      if (validColumns.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No valid columns specified'
        });
      }

      query = supabase
        .from(table)
        .select('*');

      // Add OR conditions for each column
      const orConditions = validColumns.map(col => `${col}.ilike.%${sanitizedSearchTerm}%`).join(',');
      query = query.or(orConditions);
    } else {
      // Get table structure first to search all text columns
      const structure = await getTableStructure(table);
      const textColumns = structure
        .filter(col => ['text', 'varchar', 'character varying', 'char'].some(type =>
          col.data_type.toLowerCase().includes(type)
        ))
        .map(col => col.column_name)
        .filter(col => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(col));

      if (textColumns.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No searchable text columns found in table'
        });
      }

      query = supabase
        .from(table)
        .select('*');

      // Add OR conditions for all text columns
      const orConditions = textColumns.map(col => `${col}.ilike.%${sanitizedSearchTerm}%`).join(',');
      query = query.or(orConditions);
    }

    // Limit results
    query = query.limit(100);

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    res.json({
      success: true,
      data: data || [],
      count: data ? data.length : 0,
      searchTerm: searchTerm,
      table: table
    });
  } catch (error) {
    console.error('Error executing search:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute search',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error',
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found' 
  });
});

// Start server
async function startServer() {
  try {
    // Test Supabase connection
    const isConnected = await testSupabaseConnection();
    if (!isConnected) {
      console.warn('Warning: Supabase connection test failed, but starting server anyway');
    }

    app.listen(PORT, () => {
      console.log(`Server running on http://localhost:${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`Supabase URL: ${supabaseUrl}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down server...');
  process.exit(0);
});

startServer();
