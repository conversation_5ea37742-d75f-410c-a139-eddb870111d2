/**
 * Natural Language to SQL Query Translator using Supabase
 * Translates user natural language queries into SQL and executes them
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Database schema information for query translation
 */
const DATABASE_SCHEMA = {
  users: {
    columns: ['id', 'first_name', 'last_name', 'email', 'phone', 'city', 'country', 'created_at'],
    description: 'User information including personal details and contact info'
  },
  products: {
    columns: ['id', 'name', 'description', 'category', 'price', 'stock_quantity', 'sku', 'created_at'],
    description: 'Product catalog with pricing and inventory information'
  },
  orders: {
    columns: ['id', 'user_id', 'order_number', 'total_amount', 'status', 'order_date', 'shipping_address'],
    description: 'Order records with customer and transaction details'
  }
};

/**
 * Natural Language to SQL Query Translator
 * Converts user natural language queries into SQL using pattern matching
 */
class NLQueryTranslator {
  constructor() {
    this.patterns = [
      // User queries
      {
        pattern: /show\s+(?:me\s+)?(?:all\s+)?users?/i,
        sql: 'SELECT * FROM users ORDER BY created_at DESC LIMIT 50',
        description: 'Show all users'
      },
      {
        pattern: /(?:find|get|show)\s+users?\s+(?:in|from)\s+(.+)/i,
        sql: (match) => `SELECT * FROM users WHERE city ILIKE '%${this.sanitize(match[1])}%' OR country ILIKE '%${this.sanitize(match[1])}%' LIMIT 20`,
        description: 'Find users by location'
      },
      {
        pattern: /(?:find|get|show)\s+users?\s+(?:with\s+)?(?:name|named)\s+(.+)/i,
        sql: (match) => `SELECT * FROM users WHERE first_name ILIKE '%${this.sanitize(match[1])}%' OR last_name ILIKE '%${this.sanitize(match[1])}%' LIMIT 20`,
        description: 'Find users by name'
      },

      // Product queries
      {
        pattern: /show\s+(?:me\s+)?(?:all\s+)?products?/i,
        sql: 'SELECT * FROM products ORDER BY created_at DESC LIMIT 50',
        description: 'Show all products'
      },
      {
        pattern: /(?:find|get|show)\s+products?\s+(?:in\s+)?(?:category\s+)?(.+)/i,
        sql: (match) => `SELECT * FROM products WHERE category ILIKE '%${this.sanitize(match[1])}%' OR name ILIKE '%${this.sanitize(match[1])}%' LIMIT 20`,
        description: 'Find products by category or name'
      },
      {
        pattern: /(?:find|get|show)\s+(?:cheap|affordable|budget)\s+products?/i,
        sql: 'SELECT * FROM products WHERE price < 50 ORDER BY price ASC LIMIT 20',
        description: 'Find affordable products'
      },
      {
        pattern: /(?:find|get|show)\s+(?:expensive|premium)\s+products?/i,
        sql: 'SELECT * FROM products WHERE price > 100 ORDER BY price DESC LIMIT 20',
        description: 'Find expensive products'
      },

      // Order queries
      {
        pattern: /show\s+(?:me\s+)?(?:all\s+)?orders?/i,
        sql: 'SELECT * FROM orders ORDER BY order_date DESC LIMIT 50',
        description: 'Show all orders'
      },
      {
        pattern: /(?:find|get|show)\s+(?:recent|latest)\s+orders?/i,
        sql: 'SELECT * FROM orders ORDER BY order_date DESC LIMIT 10',
        description: 'Show recent orders'
      },
      {
        pattern: /(?:find|get|show)\s+orders?\s+(?:with\s+)?status\s+(.+)/i,
        sql: (match) => `SELECT * FROM orders WHERE status = '${this.sanitize(match[1]).toLowerCase()}' ORDER BY order_date DESC LIMIT 20`,
        description: 'Find orders by status'
      },

      // General queries
      {
        pattern: /(?:count|how\s+many)\s+users?/i,
        sql: 'SELECT COUNT(*) as total_users FROM users',
        description: 'Count total users'
      },
      {
        pattern: /(?:count|how\s+many)\s+products?/i,
        sql: 'SELECT COUNT(*) as total_products FROM products',
        description: 'Count total products'
      },
      {
        pattern: /(?:count|how\s+many)\s+orders?/i,
        sql: 'SELECT COUNT(*) as total_orders FROM orders',
        description: 'Count total orders'
      }
    ];
  }

  /**
   * Sanitize input to prevent SQL injection
   */
  sanitize(input) {
    return input.replace(/['"\\;]/g, '').trim();
  }

  /**
   * Translate natural language query to SQL
   */
  translate(naturalQuery) {
    const query = naturalQuery.trim();

    for (const pattern of this.patterns) {
      const match = query.match(pattern.pattern);
      if (match) {
        const sql = typeof pattern.sql === 'function' ? pattern.sql(match) : pattern.sql;
        return {
          sql,
          description: pattern.description,
          matched: true
        };
      }
    }

    // If no pattern matches, return a helpful message
    return {
      sql: null,
      description: 'Query not recognized. Try asking about users, products, or orders.',
      matched: false,
      suggestions: [
        'Show all users',
        'Find users in New York',
        'Show all products',
        'Find products in Electronics',
        'Show recent orders',
        'Count users'
      ]
    };
  }
}

const translator = new NLQueryTranslator();

// API Routes

/**
 * Health check endpoint
 */
app.get('/api/health', async (req, res) => {
  try {
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    res.json({
      status: 'OK',
      message: 'Natural Language to SQL Translator is running',
      supabaseStatus: error ? 'Error' : 'Connected',
      tablesAvailable: Object.keys(DATABASE_SCHEMA)
    });
  } catch (error) {
    res.json({
      status: 'OK',
      message: 'Server is running',
      supabaseStatus: 'Error',
      error: error.message
    });
  }
});

/**
 * Get database schema information
 */
app.get('/api/schema', (req, res) => {
  res.json({
    success: true,
    schema: DATABASE_SCHEMA,
    description: 'Available tables and their columns for natural language queries'
  });
});

/**
 * Translate natural language query to SQL and execute
 */
app.post('/api/query', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Query is required and must be a string'
      });
    }

    // Translate natural language to SQL
    const translation = translator.translate(query);

    if (!translation.matched) {
      return res.json({
        success: false,
        error: translation.description,
        suggestions: translation.suggestions,
        originalQuery: query
      });
    }

    console.log(`Executing SQL: ${translation.sql}`);

    // Execute the SQL query using Supabase
    const { data, error } = await supabase.rpc('execute_sql', {
      query: translation.sql
    });

    if (error) {
      // If RPC doesn't exist, try to execute based on query type
      if (error.message.includes('function execute_sql') ||
          error.message.includes('does not exist')) {

        // Parse the SQL to determine the operation
        const result = await executeQueryDirectly(translation.sql);

        return res.json({
          success: true,
          data: result.data,
          sql: translation.sql,
          description: translation.description,
          originalQuery: query,
          rowCount: result.data ? result.data.length : 0
        });
      }

      throw new Error(error.message);
    }

    res.json({
      success: true,
      data: data,
      sql: translation.sql,
      description: translation.description,
      originalQuery: query,
      rowCount: data ? data.length : 0
    });

  } catch (error) {
    console.error('Error executing query:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute query',
      message: error.message
    });
  }
});

/**
 * Execute SQL query directly using Supabase client
 * This is a fallback when RPC functions are not available
 */
async function executeQueryDirectly(sql) {
  try {
    // Parse the SQL to determine the table and operation
    const sqlLower = sql.toLowerCase().trim();

    if (sqlLower.startsWith('select count(*)')) {
      // Handle COUNT queries
      if (sqlLower.includes('from users')) {
        const { count, error } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_users: count }] };
      } else if (sqlLower.includes('from products')) {
        const { count, error } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_products: count }] };
      } else if (sqlLower.includes('from orders')) {
        const { count, error } = await supabase
          .from('orders')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_orders: count }] };
      }
    }

    // Handle SELECT queries for specific tables
    if (sqlLower.includes('from users')) {
      let query = supabase.from('users').select('*');

      // Add WHERE conditions based on SQL
      if (sqlLower.includes('where')) {
        if (sqlLower.includes('city ilike') || sqlLower.includes('country ilike')) {
          const match = sql.match(/(?:city|country)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`city.ilike.%${match[1]}%,country.ilike.%${match[1]}%`);
          }
        } else if (sqlLower.includes('first_name ilike') || sqlLower.includes('last_name ilike')) {
          const match = sql.match(/(?:first_name|last_name)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`first_name.ilike.%${match[1]}%,last_name.ilike.%${match[1]}%`);
          }
        }
      }

      // Add LIMIT
      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    // Handle products queries
    if (sqlLower.includes('from products')) {
      let query = supabase.from('products').select('*');

      if (sqlLower.includes('where')) {
        if (sqlLower.includes('price <')) {
          const match = sql.match(/price\s*<\s*(\d+)/i);
          if (match) {
            query = query.lt('price', parseInt(match[1]));
          }
        } else if (sqlLower.includes('price >')) {
          const match = sql.match(/price\s*>\s*(\d+)/i);
          if (match) {
            query = query.gt('price', parseInt(match[1]));
          }
        } else if (sqlLower.includes('category ilike') || sqlLower.includes('name ilike')) {
          const match = sql.match(/(?:category|name)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`category.ilike.%${match[1]}%,name.ilike.%${match[1]}%`);
          }
        }
      }

      // Add ORDER BY
      if (sqlLower.includes('order by price asc')) {
        query = query.order('price', { ascending: true });
      } else if (sqlLower.includes('order by price desc')) {
        query = query.order('price', { ascending: false });
      } else if (sqlLower.includes('order by created_at desc')) {
        query = query.order('created_at', { ascending: false });
      }

      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    // Handle orders queries
    if (sqlLower.includes('from orders')) {
      let query = supabase.from('orders').select('*');

      if (sqlLower.includes('where')) {
        if (sqlLower.includes('status =')) {
          const match = sql.match(/status\s*=\s*'([^']+)'/i);
          if (match) {
            query = query.eq('status', match[1]);
          }
        }
      }

      if (sqlLower.includes('order by order_date desc')) {
        query = query.order('order_date', { ascending: false });
      }

      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    throw new Error('Unsupported SQL query');

  } catch (error) {
    console.error('Error executing query directly:', error);
    throw error;
  }
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    availableEndpoints: [
      'GET /api/health - Check server status',
      'GET /api/schema - Get database schema',
      'POST /api/query - Execute natural language query'
    ]
  });
});

// Start server
async function startServer() {
  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.warn('Warning: Supabase connection test failed:', error.message);
      console.warn('Server will start but queries may fail');
    } else {
      console.log('✅ Supabase connection successful');
    }

    app.listen(PORT, () => {
      console.log(`🚀 Natural Language to SQL Translator running on http://localhost:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🗄️  Database: ${supabaseUrl}`);
      console.log(`📋 Available tables: ${Object.keys(DATABASE_SCHEMA).join(', ')}`);
      console.log('');
      console.log('Try these example queries:');
      console.log('  - "Show all users"');
      console.log('  - "Find users in New York"');
      console.log('  - "Show recent orders"');
      console.log('  - "Count products"');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Natural Language to SQL Translator...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Natural Language to SQL Translator...');
  process.exit(0);
});

startServer();
