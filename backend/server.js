/**
 * Natural Language to SQL Query Translator using Supabase
 * Translates user natural language queries into SQL and executes them
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');
const OpenAI = require('openai');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize OpenAI client
const openaiApiKey = process.env.OPENAI_API_KEY;

if (!openaiApiKey) {
  console.error('Error: OPENAI_API_KEY must be set in environment variables');
  process.exit(1);
}

const openai = new OpenAI({
  apiKey: openaiApiKey
});

/**
 * Database schema information for query translation
 */
const DATABASE_SCHEMA = {
  users: {
    columns: ['id', 'first_name', 'last_name', 'email', 'phone', 'city', 'country', 'created_at'],
    description: 'User information including personal details and contact info',
    sampleData: 'Contains users like John Doe from New York, Jane Smith from Los Angeles, etc.'
  },
  products: {
    columns: ['id', 'name', 'description', 'category', 'price', 'stock_quantity', 'sku', 'created_at'],
    description: 'Product catalog with pricing and inventory information',
    sampleData: 'Contains products like Laptop Pro 15" ($1299.99), Wireless Mouse ($29.99), etc. Categories include Electronics, Accessories.'
  },
  orders: {
    columns: ['id', 'user_id', 'order_number', 'total_amount', 'status', 'order_date', 'shipping_address'],
    description: 'Order records with customer and transaction details',
    sampleData: 'Contains orders with statuses: pending, processing, shipped, delivered, cancelled. Order numbers like ORD-2024-001.'
  }
};

/**
 * OpenAI-powered Natural Language to SQL Query Translator
 * Uses GPT to dynamically convert natural language queries into SQL
 */
class OpenAINLQueryTranslator {
  constructor(openaiClient, databaseSchema) {
    this.openai = openaiClient;
    this.schema = databaseSchema;
    this.systemPrompt = this.buildSystemPrompt();
  }

  /**
   * Build the system prompt with database schema information
   */
  buildSystemPrompt() {
    const schemaDescription = Object.entries(this.schema)
      .map(([tableName, tableInfo]) => {
        return `Table: ${tableName}
Description: ${tableInfo.description}
Columns: ${tableInfo.columns.join(', ')}
Sample Data: ${tableInfo.sampleData}`;
      })
      .join('\n\n');

    return `You are a SQL query generator for a PostgreSQL database. Convert natural language questions into valid SQL queries.

DATABASE SCHEMA:
${schemaDescription}

RULES:
1. Only generate SELECT queries (no INSERT, UPDATE, DELETE, DROP, etc.)
2. Always include a LIMIT clause (max 100 rows)
3. Use ILIKE for case-insensitive text searches
4. Use proper PostgreSQL syntax
5. For date queries, use appropriate date functions
6. Return only the SQL query, no explanations or markdown
7. If the query is unclear or impossible, return "INVALID_QUERY"

EXAMPLES:
- "Show all users" → SELECT * FROM users ORDER BY created_at DESC LIMIT 50
- "Find users in New York" → SELECT * FROM users WHERE city ILIKE '%New York%' LIMIT 20
- "Count products" → SELECT COUNT(*) as total_products FROM products
- "Show expensive products" → SELECT * FROM products WHERE price > 100 ORDER BY price DESC LIMIT 20
- "Recent orders" → SELECT * FROM orders ORDER BY order_date DESC LIMIT 10

Generate only the SQL query for the following natural language question:`;
  }

  /**
   * Translate natural language query to SQL using OpenAI
   */
  async translate(naturalQuery) {
    try {
      console.log(`🤖 Translating query: "${naturalQuery}"`);

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: this.systemPrompt
          },
          {
            role: "user",
            content: naturalQuery
          }
        ],
        max_tokens: 200,
        temperature: 0.1, // Low temperature for consistent SQL generation
      });

      const sqlQuery = completion.choices[0].message.content.trim();

      console.log(`🔍 Generated SQL: ${sqlQuery}`);

      // Validate the response
      if (sqlQuery === "INVALID_QUERY" || !sqlQuery) {
        return {
          sql: null,
          description: 'Could not understand the query. Please try rephrasing.',
          matched: false,
          suggestions: [
            'Show all users',
            'Find users in New York',
            'Show all products',
            'Find cheap products',
            'Show recent orders',
            'Count users'
          ]
        };
      }

      // Basic SQL validation
      if (!this.isValidSQL(sqlQuery)) {
        return {
          sql: null,
          description: 'Generated query failed validation. Please try a different question.',
          matched: false,
          suggestions: [
            'Show all users',
            'Find products in Electronics',
            'Show recent orders',
            'Count products'
          ]
        };
      }

      return {
        sql: sqlQuery,
        description: `AI-generated SQL for: "${naturalQuery}"`,
        matched: true
      };

    } catch (error) {
      console.error('OpenAI translation error:', error);

      return {
        sql: null,
        description: 'AI translation service is currently unavailable. Please try again later.',
        matched: false,
        error: error.message,
        suggestions: [
          'Show all users',
          'Find users in New York',
          'Show all products',
          'Show recent orders'
        ]
      };
    }
  }

  /**
   * Basic SQL validation to ensure safety
   */
  isValidSQL(sql) {
    const sqlLower = sql.toLowerCase().trim();

    // Must start with SELECT
    if (!sqlLower.startsWith('select')) {
      return false;
    }

    // Must not contain dangerous keywords
    const dangerousKeywords = [
      'drop', 'delete', 'insert', 'update', 'alter', 'create',
      'truncate', 'exec', 'execute', 'sp_', 'xp_', '--', '/*'
    ];

    for (const keyword of dangerousKeywords) {
      if (sqlLower.includes(keyword)) {
        return false;
      }
    }

    // Must contain FROM clause
    if (!sqlLower.includes('from')) {
      return false;
    }

    // Must reference valid tables
    const validTables = Object.keys(this.schema);
    const hasValidTable = validTables.some(table =>
      sqlLower.includes(`from ${table}`) || sqlLower.includes(`join ${table}`)
    );

    if (!hasValidTable) {
      return false;
    }

    return true;
  }
}

const translator = new OpenAINLQueryTranslator(openai, DATABASE_SCHEMA);

// API Routes

/**
 * Health check endpoint
 */
app.get('/api/health', async (req, res) => {
  try {
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    res.json({
      status: 'OK',
      message: 'Natural Language to SQL Translator is running',
      supabaseStatus: error ? 'Error' : 'Connected',
      tablesAvailable: Object.keys(DATABASE_SCHEMA)
    });
  } catch (error) {
    res.json({
      status: 'OK',
      message: 'Server is running',
      supabaseStatus: 'Error',
      error: error.message
    });
  }
});

/**
 * Get database schema information
 */
app.get('/api/schema', (req, res) => {
  res.json({
    success: true,
    schema: DATABASE_SCHEMA,
    description: 'Available tables and their columns for natural language queries'
  });
});

/**
 * Translate natural language query to SQL and execute using OpenAI
 */
app.post('/api/query', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Query is required and must be a string'
      });
    }

    // Translate natural language to SQL using OpenAI
    const translation = await translator.translate(query);

    if (!translation.matched) {
      return res.json({
        success: false,
        error: translation.description,
        suggestions: translation.suggestions,
        originalQuery: query,
        aiError: translation.error || null
      });
    }

    console.log(`🚀 Executing AI-generated SQL: ${translation.sql}`);

    // Execute the SQL query using Supabase
    const { data, error } = await supabase.rpc('execute_sql', {
      query: translation.sql
    });

    if (error) {
      // If RPC doesn't exist, try to execute based on query type
      if (error.message.includes('function execute_sql') ||
          error.message.includes('does not exist')) {

        // Parse the SQL to determine the operation
        const result = await executeQueryDirectly(translation.sql);

        return res.json({
          success: true,
          data: result.data,
          sql: translation.sql,
          description: translation.description,
          originalQuery: query,
          rowCount: result.data ? result.data.length : 0,
          aiGenerated: true
        });
      }

      throw new Error(error.message);
    }

    res.json({
      success: true,
      data: data,
      sql: translation.sql,
      description: translation.description,
      originalQuery: query,
      rowCount: data ? data.length : 0,
      aiGenerated: true
    });

  } catch (error) {
    console.error('Error executing AI-generated query:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute AI-generated query',
      message: error.message,
      originalQuery: req.body.query
    });
  }
});

/**
 * Execute SQL query directly using Supabase client
 * This is a fallback when RPC functions are not available
 */
async function executeQueryDirectly(sql) {
  try {
    // Parse the SQL to determine the table and operation
    const sqlLower = sql.toLowerCase().trim();

    if (sqlLower.startsWith('select count(*)')) {
      // Handle COUNT queries
      if (sqlLower.includes('from users')) {
        const { count, error } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_users: count }] };
      } else if (sqlLower.includes('from products')) {
        const { count, error } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_products: count }] };
      } else if (sqlLower.includes('from orders')) {
        const { count, error } = await supabase
          .from('orders')
          .select('*', { count: 'exact', head: true });
        if (error) throw error;
        return { data: [{ total_orders: count }] };
      }
    }

    // Handle SELECT queries for specific tables
    if (sqlLower.includes('from users')) {
      let query = supabase.from('users').select('*');

      // Add WHERE conditions based on SQL
      if (sqlLower.includes('where')) {
        if (sqlLower.includes('city ilike') || sqlLower.includes('country ilike')) {
          const match = sql.match(/(?:city|country)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`city.ilike.%${match[1]}%,country.ilike.%${match[1]}%`);
          }
        } else if (sqlLower.includes('first_name ilike') || sqlLower.includes('last_name ilike')) {
          const match = sql.match(/(?:first_name|last_name)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`first_name.ilike.%${match[1]}%,last_name.ilike.%${match[1]}%`);
          }
        }
      }

      // Add LIMIT
      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    // Handle products queries
    if (sqlLower.includes('from products')) {
      let query = supabase.from('products').select('*');

      if (sqlLower.includes('where')) {
        if (sqlLower.includes('price <')) {
          const match = sql.match(/price\s*<\s*(\d+)/i);
          if (match) {
            query = query.lt('price', parseInt(match[1]));
          }
        } else if (sqlLower.includes('price >')) {
          const match = sql.match(/price\s*>\s*(\d+)/i);
          if (match) {
            query = query.gt('price', parseInt(match[1]));
          }
        } else if (sqlLower.includes('category ilike') || sqlLower.includes('name ilike')) {
          const match = sql.match(/(?:category|name)\s+ilike\s+'%([^%]+)%'/i);
          if (match) {
            query = query.or(`category.ilike.%${match[1]}%,name.ilike.%${match[1]}%`);
          }
        }
      }

      // Add ORDER BY
      if (sqlLower.includes('order by price asc')) {
        query = query.order('price', { ascending: true });
      } else if (sqlLower.includes('order by price desc')) {
        query = query.order('price', { ascending: false });
      } else if (sqlLower.includes('order by created_at desc')) {
        query = query.order('created_at', { ascending: false });
      }

      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    // Handle orders queries
    if (sqlLower.includes('from orders')) {
      let query = supabase.from('orders').select('*');

      if (sqlLower.includes('where')) {
        if (sqlLower.includes('status =')) {
          const match = sql.match(/status\s*=\s*'([^']+)'/i);
          if (match) {
            query = query.eq('status', match[1]);
          }
        }
      }

      if (sqlLower.includes('order by order_date desc')) {
        query = query.order('order_date', { ascending: false });
      }

      const limitMatch = sql.match(/limit\s+(\d+)/i);
      if (limitMatch) {
        query = query.limit(parseInt(limitMatch[1]));
      }

      const { data, error } = await query;
      if (error) throw error;
      return { data };
    }

    throw new Error('Unsupported SQL query');

  } catch (error) {
    console.error('Error executing query directly:', error);
    throw error;
  }
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    availableEndpoints: [
      'GET /api/health - Check server status',
      'GET /api/schema - Get database schema',
      'POST /api/query - Execute natural language query'
    ]
  });
});

// Start server
async function startServer() {
  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.warn('Warning: Supabase connection test failed:', error.message);
      console.warn('Server will start but queries may fail');
    } else {
      console.log('✅ Supabase connection successful');
    }

    app.listen(PORT, () => {
      console.log(`🤖 AI-Powered Natural Language to SQL Translator running on http://localhost:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🗄️  Database: ${supabaseUrl}`);
      console.log(`🧠 AI Model: GPT-3.5-turbo (OpenAI)`);
      console.log(`📋 Available tables: ${Object.keys(DATABASE_SCHEMA).join(', ')}`);
      console.log('');
      console.log('🎯 AI can understand complex queries like:');
      console.log('  - "Show me all users from California who joined this year"');
      console.log('  - "Find the most expensive products in Electronics category"');
      console.log('  - "How many orders were delivered last month?"');
      console.log('  - "Show users with gmail addresses"');
      console.log('  - "What are the top 5 selling products?"');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Natural Language to SQL Translator...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Natural Language to SQL Translator...');
  process.exit(0);
});

startServer();
