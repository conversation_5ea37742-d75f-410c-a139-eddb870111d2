-- MySQL Database Schema for Search Application
-- Creates a test database with sample tables and data

-- Create database
CREATE DATABASE IF NOT EXISTS test_db;
USE test_db;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    city VARCHAR(50),
    country VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INT DEFAULT 0,
    sku VARCHAR(50) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMES<PERSON>MP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    order_number VARCHAR(20) UNIQUE NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    shipping_address TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample data into users table
INSERT INTO users (first_name, last_name, email, phone, city, country) VALUES
('John', 'Doe', '<EMAIL>', '******-0101', 'New York', 'USA'),
('Jane', 'Smith', '<EMAIL>', '******-0102', 'Los Angeles', 'USA'),
('Mike', 'Johnson', '<EMAIL>', '******-0103', 'Chicago', 'USA'),
('Sarah', 'Williams', '<EMAIL>', '******-0104', 'Houston', 'USA'),
('David', 'Brown', '<EMAIL>', '******-0105', 'Phoenix', 'USA'),
('Lisa', 'Davis', '<EMAIL>', '******-0106', 'Philadelphia', 'USA'),
('Tom', 'Wilson', '<EMAIL>', '******-0107', 'San Antonio', 'USA'),
('Emma', 'Garcia', '<EMAIL>', '******-0108', 'San Diego', 'USA'),
('James', 'Martinez', '<EMAIL>', '******-0109', 'Dallas', 'USA'),
('Anna', 'Anderson', '<EMAIL>', '******-0110', 'San Jose', 'USA');

-- Insert sample data into products table
INSERT INTO products (name, description, category, price, stock_quantity, sku) VALUES
('Laptop Pro 15"', 'High-performance laptop with 16GB RAM and 512GB SSD', 'Electronics', 1299.99, 25, 'LAP-PRO-15'),
('Wireless Mouse', 'Ergonomic wireless mouse with precision tracking', 'Electronics', 29.99, 150, 'MSE-WRL-001'),
('Mechanical Keyboard', 'RGB backlit mechanical keyboard with blue switches', 'Electronics', 89.99, 75, 'KBD-MCH-RGB'),
('USB-C Hub', '7-in-1 USB-C hub with HDMI, USB 3.0, and SD card reader', 'Electronics', 49.99, 100, 'HUB-USC-7IN1'),
('Bluetooth Headphones', 'Noise-cancelling over-ear headphones with 30-hour battery', 'Electronics', 199.99, 50, 'HPH-BT-NC30'),
('Smartphone Case', 'Protective case for latest smartphone models', 'Accessories', 19.99, 200, 'CSE-SPH-PROT'),
('Portable Charger', '10000mAh portable battery pack with fast charging', 'Electronics', 39.99, 80, 'CHG-PORT-10K'),
('Webcam HD', '1080p HD webcam with auto-focus and built-in microphone', 'Electronics', 69.99, 60, 'CAM-HD-1080'),
('Monitor Stand', 'Adjustable monitor stand with storage compartment', 'Accessories', 34.99, 40, 'STD-MON-ADJ'),
('Cable Organizer', 'Desktop cable management system with multiple slots', 'Accessories', 14.99, 120, 'ORG-CBL-DESK');

-- Insert sample data into orders table
INSERT INTO orders (user_id, order_number, total_amount, status, shipping_address) VALUES
(1, 'ORD-2024-001', 1329.98, 'delivered', '123 Main St, New York, NY 10001'),
(2, 'ORD-2024-002', 89.99, 'shipped', '456 Oak Ave, Los Angeles, CA 90210'),
(3, 'ORD-2024-003', 249.97, 'processing', '789 Pine St, Chicago, IL 60601'),
(4, 'ORD-2024-004', 69.99, 'delivered', '321 Elm St, Houston, TX 77001'),
(5, 'ORD-2024-005', 159.98, 'pending', '654 Maple Dr, Phoenix, AZ 85001'),
(1, 'ORD-2024-006', 49.99, 'shipped', '123 Main St, New York, NY 10001'),
(6, 'ORD-2024-007', 34.99, 'delivered', '987 Cedar Ln, Philadelphia, PA 19101'),
(7, 'ORD-2024-008', 199.99, 'processing', '147 Birch Rd, San Antonio, TX 78201'),
(8, 'ORD-2024-009', 119.98, 'shipped', '258 Spruce St, San Diego, CA 92101'),
(9, 'ORD-2024-010', 14.99, 'delivered', '369 Willow Way, Dallas, TX 75201');

-- Create indexes for better search performance
CREATE INDEX idx_users_name ON users(first_name, last_name);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_city ON users(city);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_orders_number ON orders(order_number);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_date ON orders(order_date);

-- Show tables and their row counts
SELECT 'users' as table_name, COUNT(*) as row_count FROM users
UNION ALL
SELECT 'products' as table_name, COUNT(*) as row_count FROM products
UNION ALL
SELECT 'orders' as table_name, COUNT(*) as row_count FROM orders;
