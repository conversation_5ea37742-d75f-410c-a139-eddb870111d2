{"name": "mcp-sql-vue-app", "version": "1.0.0", "description": "A simple Vue.js and Node.js application for MySQL table search using MCP server", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build"}, "keywords": ["vue", "nodejs", "mysql", "mcp", "search"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}