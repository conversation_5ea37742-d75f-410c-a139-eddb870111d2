# 🤖 Natural Language to SQL Translator (Supabase)

A simple, intelligent web application that translates natural language questions into SQL queries and executes them against a PostgreSQL database using Supabase. Ask questions in plain English and get instant results!

## 🚀 Features

- **🗣️ Natural Language Interface**: Ask questions in plain English
- **🔄 Automatic SQL Translation**: Converts your questions to SQL queries
- **⚡ Instant Results**: Execute queries and see results immediately
- **📊 Smart Formatting**: Automatically formats data for easy reading
- **💡 Query Suggestions**: Get helpful suggestions when queries aren't recognized
- **📋 Database Schema Viewer**: Explore available tables and columns
- **📝 Example Queries**: Built-in examples to get you started
- **🌐 Modern Tech Stack**: Vue 3, Node.js, Express, Supabase, and Vite
- **📱 Responsive Design**: Works perfectly on desktop and mobile
- **☁️ Cloud Database**: No local database setup required

## 📋 Prerequisites

Before you begin, ensure you have the following:

- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **Supabase Account** (free) - [Sign up here](https://supabase.com)
- **npm** (comes with Node.js)

### Why Natural Language to SQL?

This application demonstrates:
- **Accessibility**: Non-technical users can query databases
- **Efficiency**: No need to learn SQL syntax
- **Pattern Recognition**: Smart translation of common query patterns
- **Educational**: See how natural language maps to SQL

### Why Supabase?

Supabase provides:
- **Managed PostgreSQL**: No local database installation required
- **Real-time capabilities**: Built-in real-time subscriptions
- **Security**: Row Level Security and built-in authentication
- **API Generation**: Automatic REST and GraphQL APIs
- **Dashboard**: Web interface for database management

## 🛠️ Installation

### Step 1: Clone or Download the Project

If you have the project files, navigate to the project directory:
```bash
cd mcp-sql-vue-app
```

### Step 2: Install Dependencies

Install all dependencies for the main project, backend, and frontend:
```bash
npm run install-all
```

This command will:
- Install root dependencies (concurrently for running both servers)
- Install backend dependencies (Express, CORS, MySQL MCP, etc.)
- Install frontend dependencies (Vue 3, Axios, Vite, etc.)

### Step 3: Set Up Supabase Database

#### Create Supabase Project
1. Go to [Supabase](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Fill in project details and create the project
4. Wait for the project to be ready (2-3 minutes)

#### Get Your Credentials
1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy your **Project URL** and **anon public** key

#### Set Up Database Schema
1. In Supabase dashboard, go to **SQL Editor**
2. Copy and paste the contents of `database/schema.sql`
3. Click "Run" to create tables and sample data

For detailed instructions, see: `database/supabase-setup.md`

### Step 4: Configure Environment Variables

Copy the example environment file and update it with your Supabase credentials:
```bash
cp backend/.env.example backend/.env
```

Edit `backend/.env`:
```env
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
PORT=3001
NODE_ENV=development
```

### Step 5: Test Your Setup (Optional but Recommended)

Before running the full application, test your Supabase connection:

```bash
cd backend
npm run test-connection
```

This will verify:
- ✅ Supabase connection is working
- ✅ Database tables exist and are accessible
- ✅ Helper functions are properly installed
- ✅ Search functionality works

If the test fails, it will provide specific guidance on what needs to be fixed.

## 🚀 Running the Application

### Development Mode (Recommended)

Start both frontend and backend servers simultaneously:
```bash
npm run dev
```

This will start:
- **Backend API**: http://localhost:3001
- **Frontend App**: http://localhost:3000

### Production Mode

Build the frontend and start the backend:
```bash
npm run build
npm run server
```

### Running Servers Separately

#### Backend only:
```bash
npm run server
```

#### Frontend only:
```bash
npm run client
```

## 📖 Usage Guide

### 1. Access the Application
Open your web browser and navigate to: http://localhost:3000

### 2. Ask Natural Language Questions
Simply type your question in plain English in the text area. The application will:
- Translate your question to SQL
- Execute the query against the database
- Display the results in a formatted table

### 3. Example Questions You Can Ask

**👥 User Queries:**
- "Show all users"
- "Find users in New York"
- "Find users named John"
- "Count users"

**📦 Product Queries:**
- "Show all products"
- "Find cheap products"
- "Find products in Electronics"
- "Show expensive products"
- "Count products"

**📋 Order Queries:**
- "Show recent orders"
- "Find orders with status delivered"
- "Show all orders"
- "Count orders"

### 4. Explore the Database
- Click **"📊 View Schema"** to see all available tables and columns
- Click **"📝 Show Examples"** to see more query examples
- Use the suggestions when your query isn't recognized

### 5. Understanding Results
- Results are automatically formatted for readability
- Currency values are displayed with $ symbols
- Long text is truncated with "..."
- The generated SQL query is shown for learning purposes

## 🏗️ Project Structure

```
mcp-sql-vue-app/
├── package.json                 # Root package.json with scripts
├── README.md                   # This file
├── backend/                    # Node.js backend
│   ├── package.json           # Backend dependencies
│   ├── server.js              # Express server with MCP integration
│   ├── .env.example           # Environment variables template
│   └── .env                   # Your environment variables (create this)
├── frontend/                   # Vue.js frontend
│   ├── package.json           # Frontend dependencies
│   ├── vite.config.js         # Vite configuration
│   ├── index.html             # HTML template
│   └── src/
│       ├── main.js            # Vue app entry point
│       ├── App.vue            # Main Vue component
│       └── style.css          # Global styles
└── database/                   # Database setup files
    ├── schema.sql             # Database schema and sample data
    └── setup.sh               # Automated setup script
```

## 🔧 API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/health` - Health check and Supabase connection status
- `GET /api/schema` - Get database schema information
- `POST /api/query` - Execute natural language query

### Natural Language Query API

```javascript
// POST /api/query
{
  "query": "Show all users in New York"
}

// Response
{
  "success": true,
  "data": [...], // Query results
  "sql": "SELECT * FROM users WHERE city ILIKE '%New York%' LIMIT 20",
  "description": "Find users by location",
  "originalQuery": "Show all users in New York",
  "rowCount": 5
}
```

### Supported Query Patterns

The translator recognizes these natural language patterns:

- **"Show all [table]"** → `SELECT * FROM table`
- **"Find users in [location]"** → Search by city/country
- **"Find users named [name]"** → Search by first/last name
- **"Find [cheap/expensive] products"** → Filter by price range
- **"Find products in [category]"** → Search by category
- **"Show recent orders"** → Order by date DESC
- **"Count [table]"** → `SELECT COUNT(*) FROM table`

## 🛡️ Security Features

- **Pattern-Based Translation**: Only predefined query patterns are allowed
- **Input Sanitization**: All user inputs are sanitized before processing
- **Query Limits**: Maximum result size limits (100 rows per query)
- **Supabase Security**: Built-in security with Row Level Security (RLS)
- **No Dynamic SQL**: Direct SQL execution is disabled by default
- **Parameterized Queries**: Protection against SQL injection via Supabase client
- **CORS Protection**: Configured for local development
- **Environment Variables**: Sensitive credentials stored securely

## 🐛 Troubleshooting

### Common Issues

#### 1. Supabase Connection Error
```
Error: Could not connect to Supabase
```
**Solution:**
- Verify your `SUPABASE_URL` and `SUPABASE_ANON_KEY` are correct
- Check that your Supabase project is active (not paused)
- Ensure you're using the anon key, not the service_role key
- Test connection at: http://localhost:3001/api/health

#### 2. Query Not Recognized Error
```
Query not recognized. Try asking about users, products, or orders.
```
**Solution:**
- Use the suggested example queries
- Click "📝 Show Examples" to see supported patterns
- Try rephrasing your question using simpler language
- Check the "📊 View Schema" to see available tables and columns

#### 3. Table Not Found Error
```
Error: relation "table_name" does not exist
```
**Solution:**
- Run the schema.sql in your Supabase SQL Editor
- Check that tables were created successfully in Table Editor
- Verify you're using the correct table names

#### 3. Frontend Build Errors
```
Error: Cannot resolve dependency
```
**Solution:**
- Delete `node_modules` and reinstall: `rm -rf frontend/node_modules && cd frontend && npm install`
- Check Node.js version: `node --version` (should be v18+)

#### 4. Port Already in Use
```
Error: Port 3000 is already in use
```
**Solution:**
- Kill the process using the port: `lsof -ti:3000 | xargs kill -9`
- Or change the port in `frontend/vite.config.js`

### Debug Mode

Enable debug logging by setting environment variable:
```bash
NODE_ENV=development npm run dev
```

## 🔄 Development

### Adding New Features

1. **Backend**: Add new routes in `backend/server.js`
2. **Frontend**: Add new components in `frontend/src/`
3. **Database**: Update schema in `database/schema.sql`

### Code Style

- **Comments**: All functions and complex logic are documented
- **Error Handling**: Comprehensive try-catch blocks
- **Responsive Design**: Mobile-first CSS approach
- **Modern JavaScript**: ES6+ features and async/await

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check the console for error messages
4. Ensure MySQL is running and accessible

## 🙏 Acknowledgments

- [Supabase](https://supabase.com/) for the excellent PostgreSQL-as-a-Service platform
- [Vue.js](https://vuejs.org/) for the reactive frontend framework
- [Express.js](https://expressjs.com/) for the backend API server
- Originally inspired by [MySQL MCP Server](https://github.com/kevinwatt/mysql-mcp) by Kevin Watt
