# MySQL Search Application with Vue.js and Node.js

A simple, modern web application that performs searches in MySQL databases using the [MySQL MCP Server](https://github.com/kevinwatt/mysql-mcp). Built with Vue.js frontend and Node.js backend following best coding practices.

## 🚀 Features

- **Simple Search Interface**: Clean, responsive Vue.js frontend
- **Real-time Database Search**: Search across MySQL tables with instant results
- **Table Management**: View available tables and their structures
- **MCP Integration**: Uses MySQL MCP server for secure database operations
- **Modern Tech Stack**: Vue 3, Node.js, Express, and Vite
- **Responsive Design**: Works on desktop and mobile devices

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **MySQL Server** (v8.0 or higher) - [Download here](https://dev.mysql.com/downloads/mysql/)
- **npm** (comes with Node.js)

### Installing MySQL

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### macOS:
```bash
brew install mysql
brew services start mysql
```

#### Windows:
Download and install from [MySQL official website](https://dev.mysql.com/downloads/mysql/)

## 🛠️ Installation

### Step 1: Clone or Download the Project

If you have the project files, navigate to the project directory:
```bash
cd mcp-sql-vue-app
```

### Step 2: Install Dependencies

Install all dependencies for the main project, backend, and frontend:
```bash
npm run install-all
```

This command will:
- Install root dependencies (concurrently for running both servers)
- Install backend dependencies (Express, CORS, MySQL MCP, etc.)
- Install frontend dependencies (Vue 3, Axios, Vite, etc.)

### Step 3: Set Up MySQL Database

#### Option A: Automated Setup (Recommended)
```bash
cd database
./setup.sh
```

Follow the prompts to enter your MySQL credentials. This will:
- Create a `test_db` database
- Create sample tables (`users`, `products`, `orders`)
- Insert sample data for testing

#### Option B: Manual Setup
```bash
mysql -u root -p < database/schema.sql
```

### Step 4: Configure Environment Variables

Copy the example environment file and update it with your MySQL credentials:
```bash
cp backend/.env.example backend/.env
```

Edit `backend/.env`:
```env
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASS=your_mysql_password
MYSQL_DB=test_db
PORT=3001
NODE_ENV=development
```

## 🚀 Running the Application

### Development Mode (Recommended)

Start both frontend and backend servers simultaneously:
```bash
npm run dev
```

This will start:
- **Backend API**: http://localhost:3001
- **Frontend App**: http://localhost:3000

### Production Mode

Build the frontend and start the backend:
```bash
npm run build
npm run server
```

### Running Servers Separately

#### Backend only:
```bash
npm run server
```

#### Frontend only:
```bash
npm run client
```

## 📖 Usage Guide

### 1. Access the Application
Open your web browser and navigate to: http://localhost:3000

### 2. Select a Table
- Click the "Refresh Tables" button to load available tables
- Select a table from the dropdown menu

### 3. Search Data
- Enter a search term in the search input field
- Click "Search" or press Enter to perform the search
- Results will be displayed in a table format below

### 4. View Table Structure
- Select a table and click "Show Structure" to see the table schema
- This displays column names, types, and other metadata

### 5. Sample Searches
Try these example searches with the sample data:

**In the `users` table:**
- Search for "John" to find users with that name
- Search for "New York" to find users in that city
- Search for "@email.com" to find all email addresses

**In the `products` table:**
- Search for "Laptop" to find laptop products
- Search for "Electronics" to find products in that category
- Search for "USB" to find USB-related products

**In the `orders` table:**
- Search for "ORD-2024" to find orders from 2024
- Search for "delivered" to find completed orders
- Search for specific order numbers

## 🏗️ Project Structure

```
mcp-sql-vue-app/
├── package.json                 # Root package.json with scripts
├── README.md                   # This file
├── backend/                    # Node.js backend
│   ├── package.json           # Backend dependencies
│   ├── server.js              # Express server with MCP integration
│   ├── .env.example           # Environment variables template
│   └── .env                   # Your environment variables (create this)
├── frontend/                   # Vue.js frontend
│   ├── package.json           # Frontend dependencies
│   ├── vite.config.js         # Vite configuration
│   ├── index.html             # HTML template
│   └── src/
│       ├── main.js            # Vue app entry point
│       ├── App.vue            # Main Vue component
│       └── style.css          # Global styles
└── database/                   # Database setup files
    ├── schema.sql             # Database schema and sample data
    └── setup.sh               # Automated setup script
```

## 🔧 API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/health` - Health check
- `GET /api/tables` - Get list of database tables
- `GET /api/tables/:tableName/structure` - Get table structure
- `POST /api/search` - Search data in tables

### Search API Example

```javascript
// POST /api/search
{
  "table": "users",
  "searchTerm": "John",
  "columns": ["first_name", "last_name", "email"] // optional
}
```

## 🛡️ Security Features

- **Read-only Operations**: Search operations use read-only transactions
- **Query Limits**: Maximum query length and result size limits
- **Parameterized Queries**: Protection against SQL injection
- **CORS Protection**: Configured for local development
- **Environment Variables**: Sensitive data stored in environment files

## 🐛 Troubleshooting

### Common Issues

#### 1. MySQL Connection Error
```
Error: Could not connect to MySQL
```
**Solution:**
- Verify MySQL is running: `sudo systemctl status mysql` (Linux) or `brew services list | grep mysql` (macOS)
- Check credentials in `backend/.env`
- Ensure MySQL port 3306 is not blocked

#### 2. MCP Server Not Starting
```
Error: MCP process not initialized
```
**Solution:**
- Install MCP server globally: `npm install -g @kevinwatt/mysql-mcp`
- Check MySQL credentials in environment variables
- Restart the backend server

#### 3. Frontend Build Errors
```
Error: Cannot resolve dependency
```
**Solution:**
- Delete `node_modules` and reinstall: `rm -rf frontend/node_modules && cd frontend && npm install`
- Check Node.js version: `node --version` (should be v18+)

#### 4. Port Already in Use
```
Error: Port 3000 is already in use
```
**Solution:**
- Kill the process using the port: `lsof -ti:3000 | xargs kill -9`
- Or change the port in `frontend/vite.config.js`

### Debug Mode

Enable debug logging by setting environment variable:
```bash
NODE_ENV=development npm run dev
```

## 🔄 Development

### Adding New Features

1. **Backend**: Add new routes in `backend/server.js`
2. **Frontend**: Add new components in `frontend/src/`
3. **Database**: Update schema in `database/schema.sql`

### Code Style

- **Comments**: All functions and complex logic are documented
- **Error Handling**: Comprehensive try-catch blocks
- **Responsive Design**: Mobile-first CSS approach
- **Modern JavaScript**: ES6+ features and async/await

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check the console for error messages
4. Ensure MySQL is running and accessible

## 🙏 Acknowledgments

- [MySQL MCP Server](https://github.com/kevinwatt/mysql-mcp) by Kevin Watt
- [Vue.js](https://vuejs.org/) for the reactive frontend framework
- [Express.js](https://expressjs.com/) for the backend API server
