# 🤖 AI-Powered Natural Language to SQL Translator

An intelligent web application that uses **OpenAI GPT-3.5-turbo** to translate complex natural language questions into SQL queries and executes them against a PostgreSQL database using Supabase. Ask sophisticated questions in plain English and get instant, accurate results!

## 🚀 Features

- **🧠 AI-Powered Translation**: Uses OpenAI GPT-3.5-turbo for intelligent SQL generation
- **🗣️ Complex Natural Language**: Understands sophisticated questions and business logic
- **🔄 Dynamic Query Generation**: No predefined patterns - AI adapts to any question
- **⚡ Instant Results**: Execute AI-generated queries and see results immediately
- **📊 Smart Formatting**: Automatically formats data for easy reading
- **💡 Intelligent Suggestions**: AI provides contextual suggestions when queries need clarification
- **📋 Database Schema Awareness**: AI understands your database structure
- **🛡️ Built-in Safety**: SQL validation and injection prevention
- **🌐 Modern Tech Stack**: Vue 3, Node.js, Express, OpenAI API, Supabase, and Vite
- **📱 Responsive Design**: Works perfectly on desktop and mobile
- **☁️ Cloud Database**: No local database setup required

## 📋 Prerequisites

Before you begin, ensure you have the following:

- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **OpenAI API Key** - [Get one here](https://platform.openai.com/api-keys)
- **Supabase Account** (free) - [Sign up here](https://supabase.com)
- **npm** (comes with Node.js)

### Why AI-Powered Natural Language to SQL?

This application demonstrates:
- **True Intelligence**: AI understands context, not just patterns
- **Unlimited Flexibility**: Handle any question, not just predefined patterns
- **Business Logic Understanding**: AI grasps concepts like "out of stock", "top customers", etc.
- **Learning Capability**: AI adapts to your specific database and terminology
- **Educational**: See how advanced AI translates complex language to SQL

### Why Supabase?

Supabase provides:
- **Managed PostgreSQL**: No local database installation required
- **Real-time capabilities**: Built-in real-time subscriptions
- **Security**: Row Level Security and built-in authentication
- **API Generation**: Automatic REST and GraphQL APIs
- **Dashboard**: Web interface for database management

## 🛠️ Installation

### Step 1: Clone or Download the Project

If you have the project files, navigate to the project directory:
```bash
cd mcp-sql-vue-app
```

### Step 2: Install Dependencies

Install all dependencies for the main project, backend, and frontend:
```bash
npm run install-all
```

This command will:
- Install root dependencies (concurrently for running both servers)
- Install backend dependencies (Express, CORS, MySQL MCP, etc.)
- Install frontend dependencies (Vue 3, Axios, Vite, etc.)

### Step 3: Set Up Supabase Database

#### Create Supabase Project
1. Go to [Supabase](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Fill in project details and create the project
4. Wait for the project to be ready (2-3 minutes)

#### Get Your Credentials
1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy your **Project URL** and **anon public** key

#### Set Up Database Schema
1. In Supabase dashboard, go to **SQL Editor**
2. Copy and paste the contents of `database/schema.sql`
3. Click "Run" to create tables and sample data

For detailed instructions, see: `database/supabase-setup.md`

### Step 4: Configure Environment Variables

Copy the example environment file and update it with your credentials:
```bash
cp backend/.env.example backend/.env
```

Edit `backend/.env`:
```env
# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Server Configuration
PORT=3001
NODE_ENV=development
```

**Getting your OpenAI API Key:**
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign up or log in to your account
3. Click "Create new secret key"
4. Copy the key and add it to your `.env` file

### Step 5: Test Your Setup (Optional but Recommended)

Before running the full application, test your Supabase connection:

```bash
cd backend
npm run test-connection
```

This will verify:
- ✅ Supabase connection is working
- ✅ Database tables exist and are accessible
- ✅ Helper functions are properly installed
- ✅ Search functionality works

If the test fails, it will provide specific guidance on what needs to be fixed.

## 🚀 Running the Application

### Development Mode (Recommended)

Start both frontend and backend servers simultaneously:
```bash
npm run dev
```

This will start:
- **Backend API**: http://localhost:3001
- **Frontend App**: http://localhost:3000

### Production Mode

Build the frontend and start the backend:
```bash
npm run build
npm run server
```

### Running Servers Separately

#### Backend only:
```bash
npm run server
```

#### Frontend only:
```bash
npm run client
```

## 📖 Usage Guide

### 1. Access the Application
Open your web browser and navigate to: http://localhost:3000

### 2. Ask Natural Language Questions
Simply type your question in plain English in the text area. The application will:
- Translate your question to SQL
- Execute the query against the database
- Display the results in a formatted table

### 3. Example Questions You Can Ask

**🤖 The AI can understand complex, natural questions:**

**👥 Advanced User Queries:**
- "Show me users from California who joined this year"
- "Find users with gmail email addresses"
- "Who are our customers from New York?"
- "How many users signed up last month?"

**📦 Intelligent Product Analysis:**
- "What are the most expensive products in Electronics?"
- "Show me the top 5 cheapest products"
- "Which products are out of stock?"
- "Find products with 'wireless' in the name"

**📋 Smart Order Intelligence:**
- "How many orders were delivered last month?"
- "Show orders with total amount greater than $100"
- "Which users have placed the most orders?"
- "What's the average order value?"

**🧠 Business Logic Understanding:**
- "Show me our best customers"
- "What are the trending products?"
- "Find incomplete orders"
- "Who hasn't ordered anything recently?"

### 4. Explore the Database
- Click **"📊 View Schema"** to see all available tables and columns
- Click **"📝 Show Examples"** to see more query examples
- Use the suggestions when your query isn't recognized

### 5. Understanding Results
- Results are automatically formatted for readability
- Currency values are displayed with $ symbols
- Long text is truncated with "..."
- The generated SQL query is shown for learning purposes

## 🏗️ Project Structure

```
mcp-sql-vue-app/
├── package.json                 # Root package.json with scripts
├── README.md                   # This file
├── backend/                    # Node.js backend
│   ├── package.json           # Backend dependencies
│   ├── server.js              # Express server with MCP integration
│   ├── .env.example           # Environment variables template
│   └── .env                   # Your environment variables (create this)
├── frontend/                   # Vue.js frontend
│   ├── package.json           # Frontend dependencies
│   ├── vite.config.js         # Vite configuration
│   ├── index.html             # HTML template
│   └── src/
│       ├── main.js            # Vue app entry point
│       ├── App.vue            # Main Vue component
│       └── style.css          # Global styles
└── database/                   # Database setup files
    ├── schema.sql             # Database schema and sample data
    └── setup.sh               # Automated setup script
```

## 🔧 API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/health` - Health check and Supabase connection status
- `GET /api/schema` - Get database schema information
- `POST /api/query` - Execute natural language query

### Natural Language Query API

```javascript
// POST /api/query
{
  "query": "Show all users in New York"
}

// Response
{
  "success": true,
  "data": [...], // Query results
  "sql": "SELECT * FROM users WHERE city ILIKE '%New York%' LIMIT 20",
  "description": "Find users by location",
  "originalQuery": "Show all users in New York",
  "rowCount": 5
}
```

### AI Translation Capabilities

The OpenAI-powered translator can handle:

- **Complex Conditions**: "users from California who joined this year"
- **Business Logic**: "products that are out of stock", "best customers"
- **Aggregations**: "average order value", "top 5 products"
- **Date Intelligence**: "last month", "this year", "recently"
- **Comparisons**: "greater than $100", "most expensive"
- **Pattern Matching**: "gmail addresses", "wireless products"
- **Relationships**: "users who haven't ordered", "orders by user"
- **Superlatives**: "best", "worst", "highest", "lowest"

## 🛡️ Security Features

- **AI-Generated SQL Validation**: All AI-generated queries are validated before execution
- **SELECT-Only Queries**: AI is instructed to generate only SELECT statements
- **Dangerous Keyword Filtering**: Blocks DROP, DELETE, INSERT, UPDATE, etc.
- **Query Limits**: Maximum result size limits (100 rows per query)
- **Table Validation**: Only allows queries against known tables
- **Supabase Security**: Built-in security with Row Level Security (RLS)
- **OpenAI API Security**: Secure API communication with OpenAI
- **Input Sanitization**: All user inputs are processed safely
- **CORS Protection**: Configured for local development
- **Environment Variables**: Sensitive credentials stored securely

## 🐛 Troubleshooting

### Common Issues

#### 1. Supabase Connection Error
```
Error: Could not connect to Supabase
```
**Solution:**
- Verify your `SUPABASE_URL` and `SUPABASE_ANON_KEY` are correct
- Check that your Supabase project is active (not paused)
- Ensure you're using the anon key, not the service_role key
- Test connection at: http://localhost:3001/api/health

#### 2. Query Not Recognized Error
```
Query not recognized. Try asking about users, products, or orders.
```
**Solution:**
- Use the suggested example queries
- Click "📝 Show Examples" to see supported patterns
- Try rephrasing your question using simpler language
- Check the "📊 View Schema" to see available tables and columns

#### 3. Table Not Found Error
```
Error: relation "table_name" does not exist
```
**Solution:**
- Run the schema.sql in your Supabase SQL Editor
- Check that tables were created successfully in Table Editor
- Verify you're using the correct table names

#### 3. Frontend Build Errors
```
Error: Cannot resolve dependency
```
**Solution:**
- Delete `node_modules` and reinstall: `rm -rf frontend/node_modules && cd frontend && npm install`
- Check Node.js version: `node --version` (should be v18+)

#### 4. Port Already in Use
```
Error: Port 3000 is already in use
```
**Solution:**
- Kill the process using the port: `lsof -ti:3000 | xargs kill -9`
- Or change the port in `frontend/vite.config.js`

### Debug Mode

Enable debug logging by setting environment variable:
```bash
NODE_ENV=development npm run dev
```

## 🔄 Development

### Adding New Features

1. **Backend**: Add new routes in `backend/server.js`
2. **Frontend**: Add new components in `frontend/src/`
3. **Database**: Update schema in `database/schema.sql`

### Code Style

- **Comments**: All functions and complex logic are documented
- **Error Handling**: Comprehensive try-catch blocks
- **Responsive Design**: Mobile-first CSS approach
- **Modern JavaScript**: ES6+ features and async/await

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check the console for error messages
4. Ensure MySQL is running and accessible

## 🙏 Acknowledgments

- [Supabase](https://supabase.com/) for the excellent PostgreSQL-as-a-Service platform
- [Vue.js](https://vuejs.org/) for the reactive frontend framework
- [Express.js](https://expressjs.com/) for the backend API server
- Originally inspired by [MySQL MCP Server](https://github.com/kevinwatt/mysql-mcp) by Kevin Watt
