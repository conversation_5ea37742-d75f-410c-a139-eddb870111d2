{"name": "mcp-sql-frontend", "version": "1.0.0", "description": "Vue.js frontend for MySQL search application", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "axios": "^1.6.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0"}, "keywords": ["vue", "frontend", "search", "mysql"], "author": "Your Name", "license": "MIT"}