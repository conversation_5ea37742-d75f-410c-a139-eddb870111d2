<template>
  <div class="container">
    <!-- Header Section -->
    <header class="header">
      <h1>MySQL Search Application</h1>
      <p>Search your MySQL database using MCP server integration</p>
    </header>

    <!-- Search Section -->
    <section class="search-section">
      <h2>Database Search</h2>

      <!-- Table Selection -->
      <div class="form-group">
        <label for="tableSelect">Select Table:</label>
        <select
          id="tableSelect"
          v-model="selectedTable"
          @change="onTableChange"
          class="form-control"
          :disabled="loading"
        >
          <option value="">-- Select a table --</option>
          <option
            v-for="table in tables"
            :key="table"
            :value="table"
          >
            {{ table }}
          </option>
        </select>
      </div>

      <!-- Search Input -->
      <div class="form-group">
        <label for="searchInput">Search Term:</label>
        <input
          id="searchInput"
          type="text"
          v-model="searchTerm"
          @keyup.enter="performSearch"
          placeholder="Enter search term..."
          class="form-control"
          :disabled="loading || !selectedTable"
        />
      </div>

      <!-- Action Buttons -->
      <div class="form-group">
        <button
          @click="performSearch"
          :disabled="loading || !selectedTable || !searchTerm.trim()"
          class="btn btn-primary"
        >
          {{ loading ? 'Searching...' : 'Search' }}
        </button>

        <button
          @click="loadTables"
          :disabled="loading"
          class="btn btn-secondary"
        >
          Refresh Tables
        </button>

        <button
          @click="showTableStructure"
          :disabled="loading || !selectedTable"
          class="btn btn-secondary"
        >
          Show Structure
        </button>
      </div>

      <!-- Status Messages -->
      <div v-if="error" class="error">
        <strong>Error:</strong> {{ error }}
      </div>

      <div v-if="successMessage" class="success">
        {{ successMessage }}
      </div>
    </section>

    <!-- Results Section -->
    <section v-if="searchResults.length > 0 || tableStructure.length > 0" class="results-section">
      <h2>Results</h2>

      <!-- Search Results -->
      <div v-if="searchResults.length > 0">
        <div class="info">
          Found {{ searchResults.length }} result(s) in table "{{ selectedTable }}"
          <br>
          <small>Query: {{ lastQuery }}</small>
        </div>

        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th v-for="column in searchColumns" :key="column">
                  {{ column }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in searchResults" :key="index">
                <td v-for="column in searchColumns" :key="column">
                  {{ row[column] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Table Structure -->
      <div v-if="tableStructure.length > 0">
        <h3>Table Structure: {{ selectedTable }}</h3>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>Field</th>
                <th>Type</th>
                <th>Null</th>
                <th>Key</th>
                <th>Default</th>
                <th>Extra</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(field, index) in tableStructure" :key="index">
                <td>{{ field.Field }}</td>
                <td>{{ field.Type }}</td>
                <td>{{ field.Null }}</td>
                <td>{{ field.Key }}</td>
                <td>{{ field.Default || 'NULL' }}</td>
                <td>{{ field.Extra }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- Loading State -->
    <div v-if="loading" class="loading">
      <p>Loading...</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'

export default {
  name: 'App',
  setup() {
    // Reactive data
    const tables = ref([])
    const selectedTable = ref('')
    const searchTerm = ref('')
    const searchResults = ref([])
    const searchColumns = ref([])
    const tableStructure = ref([])
    const loading = ref(false)
    const error = ref('')
    const successMessage = ref('')
    const lastQuery = ref('')

    // API base URL
    const API_BASE = '/api'

    /**
     * Clear messages after a delay
     */
    const clearMessages = () => {
      setTimeout(() => {
        error.value = ''
        successMessage.value = ''
      }, 5000)
    }

    /**
     * Load available tables from the database
     */
    const loadTables = async () => {
      loading.value = true
      error.value = ''

      try {
        const response = await axios.get(`${API_BASE}/tables`)

        if (response.data.success) {
          // Extract table names from Supabase response
          tables.value = response.data.data || []
          successMessage.value = `Loaded ${tables.value.length} tables`
        } else {
          error.value = response.data.error || 'Failed to load tables'
        }
      } catch (err) {
        error.value = `Network error: ${err.message}`
        console.error('Error loading tables:', err)
      } finally {
        loading.value = false
        clearMessages()
      }
    }

    /**
     * Handle table selection change
     */
    const onTableChange = () => {
      searchResults.value = []
      tableStructure.value = []
      searchTerm.value = ''
      lastQuery.value = ''
    }

    /**
     * Show table structure
     */
    const showTableStructure = async () => {
      if (!selectedTable.value) return

      loading.value = true
      error.value = ''

      try {
        const response = await axios.get(`${API_BASE}/tables/${selectedTable.value}/structure`)

        if (response.data.success) {
          // Parse structure data from Supabase response
          tableStructure.value = response.data.data.map(col => ({
            Field: col.column_name,
            Type: col.data_type,
            Null: col.is_nullable === 'YES' ? 'YES' : 'NO',
            Key: '', // PostgreSQL doesn't return key info in this query
            Default: col.column_default || 'NULL',
            Extra: ''
          }))
          searchResults.value = [] // Clear search results when showing structure
          successMessage.value = `Loaded structure for table "${selectedTable.value}"`
        } else {
          error.value = response.data.error || 'Failed to load table structure'
        }
      } catch (err) {
        error.value = `Network error: ${err.message}`
        console.error('Error loading table structure:', err)
      } finally {
        loading.value = false
        clearMessages()
      }
    }

    /**
     * Parse table structure from MCP response text
     * @param {string} structureText - Raw structure text from MCP
     * @returns {Array} Parsed structure array
     */
    const parseTableStructure = (structureText) => {
      // This is a simplified parser - adjust based on actual MCP response format
      const lines = structureText.split('\n').filter(line => line.trim())
      return lines.map(line => {
        const parts = line.split('\t') // Assuming tab-separated values
        return {
          Field: parts[0] || '',
          Type: parts[1] || '',
          Null: parts[2] || '',
          Key: parts[3] || '',
          Default: parts[4] || '',
          Extra: parts[5] || ''
        }
      })
    }

    /**
     * Perform search in the selected table
     */
    const performSearch = async () => {
      if (!selectedTable.value || !searchTerm.value.trim()) return

      loading.value = true
      error.value = ''
      tableStructure.value = [] // Clear structure when searching

      try {
        const searchData = {
          table: selectedTable.value,
          searchTerm: searchTerm.value.trim()
        }

        const response = await axios.post(`${API_BASE}/search`, searchData)

        if (response.data.success) {
          const results = response.data.data || []
          searchResults.value = results

          // Extract column names from the first result
          if (results.length > 0) {
            searchColumns.value = Object.keys(results[0])
          } else {
            searchColumns.value = []
          }

          lastQuery.value = `Search in ${selectedTable.value} for "${searchTerm.value}"`
          successMessage.value = `Search completed. Found ${searchResults.value.length} results.`
        } else {
          error.value = response.data.error || 'Search failed'
          searchResults.value = []
          searchColumns.value = []
        }
      } catch (err) {
        error.value = `Network error: ${err.message}`
        searchResults.value = []
        searchColumns.value = []
        console.error('Error performing search:', err)
      } finally {
        loading.value = false
        clearMessages()
      }
    }

    /**
     * Parse search results from MCP response text
     * @param {string} resultsText - Raw results text from MCP
     * @returns {Object} Parsed results with columns and rows
     */
    const parseSearchResults = (resultsText) => {
      const lines = resultsText.split('\n').filter(line => line.trim())

      if (lines.length === 0) {
        return { columns: [], rows: [] }
      }

      // First line contains column headers
      const columns = lines[0].split('\t')

      // Remaining lines contain data
      const rows = lines.slice(1).map(line => {
        const values = line.split('\t')
        const row = {}
        columns.forEach((col, index) => {
          row[col] = values[index] || ''
        })
        return row
      })

      return { columns, rows }
    }

    // Load tables when component mounts
    onMounted(() => {
      loadTables()
    })

    return {
      tables,
      selectedTable,
      searchTerm,
      searchResults,
      searchColumns,
      tableStructure,
      loading,
      error,
      successMessage,
      lastQuery,
      loadTables,
      onTableChange,
      showTableStructure,
      performSearch
    }
  }
}
</script>
