<template>
  <div class="container">
    <!-- Header Section -->
    <header class="header">
      <h1>🤖 Natural Language to SQL</h1>
      <p>Ask questions about your database in plain English</p>
    </header>

    <!-- Query Section -->
    <section class="query-section">
      <h2>Ask Your Question</h2>

      <!-- Natural Language Input -->
      <div class="form-group">
        <label for="queryInput">What would you like to know?</label>
        <textarea
          id="queryInput"
          v-model="naturalQuery"
          @keyup.enter="executeQuery"
          placeholder="Try asking: 'Show all users', 'Find users in New York', 'Count products', 'Show recent orders'..."
          class="form-control query-input"
          :disabled="loading"
          rows="3"
        ></textarea>
      </div>

      <!-- Action Buttons -->
      <div class="form-group">
        <button
          @click="executeQuery"
          :disabled="loading || !naturalQuery.trim()"
          class="btn btn-primary"
        >
          {{ loading ? 'Processing...' : '🔍 Ask Database' }}
        </button>

        <button
          @click="showExamples"
          :disabled="loading"
          class="btn btn-secondary"
        >
          📝 Show Examples
        </button>

        <button
          @click="showSchema"
          :disabled="loading"
          class="btn btn-secondary"
        >
          📊 View Schema
        </button>
      </div>

      <!-- Status Messages -->
      <div v-if="error" class="error">
        <strong>❌ Error:</strong> {{ error }}
        <div v-if="suggestions && suggestions.length > 0" class="suggestions">
          <p><strong>Try these instead:</strong></p>
          <ul>
            <li v-for="suggestion in suggestions" :key="suggestion">
              <button @click="useSuggestion(suggestion)" class="suggestion-btn">
                {{ suggestion }}
              </button>
            </li>
          </ul>
        </div>
      </div>

      <div v-if="successMessage" class="success">
        ✅ {{ successMessage }}
      </div>

      <!-- SQL Translation Display -->
      <div v-if="generatedSQL" class="sql-display">
        <h3>Generated SQL:</h3>
        <code>{{ generatedSQL }}</code>
      </div>
    </section>

    <!-- Results Section -->
    <section v-if="queryResults.length > 0 || showingSchema || showingExamples" class="results-section">

      <!-- Query Results -->
      <div v-if="queryResults.length > 0">
        <h2>📋 Results</h2>
        <div class="info">
          Found {{ queryResults.length }} result(s)
          <br>
          <small><strong>Your question:</strong> "{{ lastQuery }}"</small>
          <br>
          <small><strong>SQL executed:</strong> {{ generatedSQL }}</small>
        </div>

        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th v-for="column in resultColumns" :key="column">
                  {{ formatColumnName(column) }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in queryResults" :key="index">
                <td v-for="column in resultColumns" :key="column">
                  {{ formatCellValue(row[column]) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Database Schema -->
      <div v-if="showingSchema">
        <h2>📊 Database Schema</h2>
        <div v-for="(table, tableName) in databaseSchema" :key="tableName" class="schema-table">
          <h3>{{ tableName }}</h3>
          <p class="table-description">{{ table.description }}</p>
          <div class="columns-list">
            <span v-for="column in table.columns" :key="column" class="column-tag">
              {{ column }}
            </span>
          </div>
        </div>
      </div>

      <!-- Examples -->
      <div v-if="showingExamples">
        <h2>📝 Example Queries</h2>
        <div class="examples-grid">
          <div v-for="example in exampleQueries" :key="example.query" class="example-card">
            <h4>{{ example.category }}</h4>
            <p class="example-query">{{ example.query }}</p>
            <p class="example-description">{{ example.description }}</p>
            <button @click="useExample(example.query)" class="btn btn-small">
              Try This
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Loading State -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>Processing your question...</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'

export default {
  name: 'NaturalLanguageSQL',
  setup() {
    // Reactive data
    const naturalQuery = ref('')
    const queryResults = ref([])
    const resultColumns = ref([])
    const loading = ref(false)
    const error = ref('')
    const successMessage = ref('')
    const lastQuery = ref('')
    const generatedSQL = ref('')
    const suggestions = ref([])
    const databaseSchema = ref({})
    const showingSchema = ref(false)
    const showingExamples = ref(false)

    // API base URL
    const API_BASE = '/api'

    // Example queries
    const exampleQueries = ref([
      {
        category: 'User Queries',
        query: 'Show all users',
        description: 'Display all users in the database'
      },
      {
        category: 'User Queries',
        query: 'Find users in New York',
        description: 'Search for users by location'
      },
      {
        category: 'User Queries',
        query: 'Find users named John',
        description: 'Search for users by name'
      },
      {
        category: 'Product Queries',
        query: 'Show all products',
        description: 'Display all products in the catalog'
      },
      {
        category: 'Product Queries',
        query: 'Find cheap products',
        description: 'Show affordable products under $50'
      },
      {
        category: 'Product Queries',
        query: 'Find products in Electronics',
        description: 'Search products by category'
      },
      {
        category: 'Order Queries',
        query: 'Show recent orders',
        description: 'Display the latest orders'
      },
      {
        category: 'Order Queries',
        query: 'Find orders with status delivered',
        description: 'Search orders by status'
      },
      {
        category: 'Statistics',
        query: 'Count users',
        description: 'Get total number of users'
      },
      {
        category: 'Statistics',
        query: 'Count products',
        description: 'Get total number of products'
      }
    ])

    /**
     * Clear messages after a delay
     */
    const clearMessages = () => {
      setTimeout(() => {
        error.value = ''
        successMessage.value = ''
      }, 5000)
    }

    /**
     * Execute natural language query
     */
    const executeQuery = async () => {
      if (!naturalQuery.value.trim()) return

      loading.value = true
      error.value = ''
      successMessage.value = ''
      suggestions.value = []
      showingSchema.value = false
      showingExamples.value = false

      try {
        const response = await axios.post(`${API_BASE}/query`, {
          query: naturalQuery.value.trim()
        })

        if (response.data.success) {
          queryResults.value = response.data.data || []
          generatedSQL.value = response.data.sql || ''
          lastQuery.value = response.data.originalQuery || naturalQuery.value

          // Extract column names from results
          if (queryResults.value.length > 0) {
            resultColumns.value = Object.keys(queryResults.value[0])
          } else {
            resultColumns.value = []
          }

          successMessage.value = `Query executed successfully! Found ${queryResults.value.length} result(s).`
        } else {
          error.value = response.data.error || 'Query failed'
          suggestions.value = response.data.suggestions || []
          queryResults.value = []
          resultColumns.value = []
          generatedSQL.value = ''
        }
      } catch (err) {
        error.value = `Network error: ${err.message}`
        queryResults.value = []
        resultColumns.value = []
        generatedSQL.value = ''
        console.error('Error executing query:', err)
      } finally {
        loading.value = false
        clearMessages()
      }
    }

    /**
     * Load database schema
     */
    const loadSchema = async () => {
      try {
        const response = await axios.get(`${API_BASE}/schema`)
        if (response.data.success) {
          databaseSchema.value = response.data.schema || {}
        }
      } catch (err) {
        console.error('Error loading schema:', err)
      }
    }

    /**
     * Show database schema
     */
    const showSchema = () => {
      showingSchema.value = !showingSchema.value
      showingExamples.value = false
      queryResults.value = []
      error.value = ''
      successMessage.value = ''
    }

    /**
     * Show example queries
     */
    const showExamples = () => {
      showingExamples.value = !showingExamples.value
      showingSchema.value = false
      queryResults.value = []
      error.value = ''
      successMessage.value = ''
    }

    /**
     * Use a suggestion
     */
    const useSuggestion = (suggestion) => {
      naturalQuery.value = suggestion
      executeQuery()
    }

    /**
     * Use an example query
     */
    const useExample = (example) => {
      naturalQuery.value = example
      showingExamples.value = false
      executeQuery()
    }

    /**
     * Format column names for display
     */
    const formatColumnName = (columnName) => {
      return columnName
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
    }

    /**
     * Format cell values for display
     */
    const formatCellValue = (value) => {
      if (value === null || value === undefined) {
        return 'NULL'
      }
      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No'
      }
      if (typeof value === 'number') {
        // Format currency if it looks like a price
        if (value > 0 && value < 10000 && value.toString().includes('.')) {
          return `$${value.toFixed(2)}`
        }
        return value.toLocaleString()
      }
      if (typeof value === 'string' && value.length > 50) {
        return value.substring(0, 50) + '...'
      }
      return value
    }

    // Load schema when component mounts
    onMounted(() => {
      loadSchema()
    })

    return {
      naturalQuery,
      queryResults,
      resultColumns,
      loading,
      error,
      successMessage,
      lastQuery,
      generatedSQL,
      suggestions,
      databaseSchema,
      showingSchema,
      showingExamples,
      exampleQueries,
      executeQuery,
      showSchema,
      showExamples,
      useSuggestion,
      useExample,
      formatColumnName,
      formatCellValue
    }
  }
}
</script>
